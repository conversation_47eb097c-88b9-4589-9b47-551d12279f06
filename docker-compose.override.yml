# Docker Compose override for development
# This file extends docker-compose.yml with development-specific configurations

services:
  financial-indicator-daemon:
    # Mount source code for development (optional)
    volumes:
      - ./scripts:/app/scripts:ro
      # Uncomment below for live code reloading during development
      # - ./build/libs:/app/build/libs:ro
    
    # Additional environment variables for development
    environment:
      # Enable debug logging for Python integration
      - LOGGING_LEVEL_COM_TRADING_FINANCIALINDICATORDAEMON_SERVICE_YAHOO=DEBUG
      
      # Python environment settings
      - PYTHONPATH=/app
      - PYTHON_UNBUFFERED=1
      
      # Yahoo Finance specific settings (if needed)
      - YAHOO_FINANCE_TIMEOUT=30
      - YAHOO_FINANCE_RETRY_COUNT=3
    
    # Override health check for development (more frequent checks)
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6601/actuator/health"]
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 30s

  # Test service to verify Python yfinance integration
  python-yfinance-test:
    build:
      context: .
      dockerfile: test/Dockerfile.test
    container_name: python_yfinance_test
    networks:
      - docker-network
    profiles:
      - test
    command: >
      bash -c "
        echo 'Testing Python yfinance integration...' &&
        source venv/bin/activate &&
        python3 scripts/test_yahoo_finance.py &&
        echo 'Testing AAPL data fetch...' &&
        python3 scripts/yahoo_finance_fetcher.py AAPL --start 2024-01-01 --end 2024-01-05 &&
        echo 'All tests completed successfully!'
      "
