# Test Dockerfile to verify Python yfinance integration works in Docker
FROM eclipse-temurin:17-jre

# Install Python 3, pip, curl and other dependencies for Yahoo Finance integration
RUN apt-get update && apt-get install -y \
    curl \
    python3 \
    python3-pip \
    python3-venv \
    && rm -rf /var/lib/apt/lists/*

# Create app user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Copy Python scripts and requirements
COPY scripts/ scripts/

# Create Python virtual environment and install dependencies
RUN python3 -m venv venv && \
    . venv/bin/activate && \
    pip install --no-cache-dir -r scripts/requirements.txt

# Change ownership to app user (including Python environment)
RUN chown -R appuser:appuser /app

# Switch to app user
USER appuser

# Test script to verify Python yfinance works
RUN . venv/bin/activate && python3 scripts/test_yahoo_finance.py

# Test fetching actual data
RUN . venv/bin/activate && python3 scripts/yahoo_finance_fetcher.py AAPL --start 2024-01-01 --end 2024-01-05

# Default command
CMD ["bash", "-c", "source venv/bin/activate && python3 scripts/test_yahoo_finance.py"]
