package com.trading.financialindicatordaemon.amqp.listener;

import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.client.indicatorapi.IndicatorData;
import com.trading.financialindicatordaemon.service.indicator.IndicatorDataService;
import com.trading.financialindicatordaemon.service.stock.StockCandleDataService;
import com.trading.financialindicatordaemon.service.yahoo.YahooFinanceService;
import com.trading.financialindicatordaemon.service.yahoo.YahooStockCandle;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.Mockito.verify;

public class CreateStockIndicatorDataListenerTest extends BaseTest {

    @Autowired
    private CreateStockIndicatorDataListener createStockIndicatorDataListener;

    @Autowired
    private IndicatorDataService indicatorDataService;

    @Autowired
    private StockCandleDataService stockCandleDataService;

    @Autowired
    private YahooFinanceService yahooFinanceService;

    @MockBean
    private com.rabbitmq.client.Channel channel;

    @Test
    public void handleCreateStockIndicatorData_shouldCalculateIndicators() throws Exception {
        List<YahooStockCandle> candles = yahooFinanceService.getStockCandles("TSLA", 1735430400, 1751155200);
        stockCandleDataService.insertYahoo(candles);

        createStockIndicatorDataListener.handleCreateStockIndicatorData(new HashMap<>(), channel, 1L, null);

        verify(channel).basicAck(1L, false);

        List<IndicatorData> indicatorData = indicatorDataService.find("TSLA", "USD");
        assertThat(indicatorData).isNotEmpty();
    }
}
