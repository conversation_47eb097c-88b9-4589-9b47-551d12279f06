package com.trading.financialindicatordaemon.controller;

import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.service.mining.DataMiningService;
import com.trading.financialindicatordaemon.service.indicator.IndicatorDataService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static com.trading.financialindicatordaemon.config.AppConfig.USD_CURRENCY_ID;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@AutoConfigureMockMvc
public class IndicatorControllerTest extends BaseTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private IndicatorDataService indicatorDataService;
    @Autowired
    private DataMiningService dataMiningService;

    @Test
    public void getIndicatorData_shouldReturnDataWhenExists() throws Exception {
        dataMiningService.mineMappings();
        dataMiningService.mineSymbols(List.of("SOL"), USD_CURRENCY_ID);
        indicatorDataService.calculateForCrypto("SOL", "USD");

        mockMvc.perform(get("/api/v1/crypto/indicators")
                        .param("symbol", "SOL")
                        .param("conversionCurrency", "USD")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1893));
    }

    @Test
    public void getIndicatorData_shouldReturnEmptyListWhenNoDataExists() throws Exception {
        mockMvc.perform(get("/api/v1/crypto/indicators")
                        .param("symbol", "NONEXISTENT")
                        .param("conversionCurrency", "USD")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(0));
    }

    @Test
    public void getIndicatorData_shouldHandleDifferentCurrencyPairs() throws Exception {
        dataMiningService.mineMappings();
        dataMiningService.mineSymbols(List.of("SOL"), USD_CURRENCY_ID);
        indicatorDataService.calculateForCrypto("SOL", "USD");

        mockMvc.perform(get("/api/v1/crypto/indicators")
                        .param("symbol", "SOL")
                        .param("conversionCurrency", "BTC")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(0));
    }
}
