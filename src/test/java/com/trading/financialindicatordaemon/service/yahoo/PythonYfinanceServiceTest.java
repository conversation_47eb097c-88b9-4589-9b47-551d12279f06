package com.trading.financialindicatordaemon.service.yahoo;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test for PythonYfinanceService to verify Python yfinance integration
 */
class PythonYfinanceServiceTest {

    private static final Logger logger = LoggerFactory.getLogger(PythonYfinanceServiceTest.class);
    
    private final PythonYfinanceService pythonYfinanceService = new PythonYfinanceService();

    @Test
    void testIsAvailable() {
        logger.info("Testing if Python yfinance is available...");
        boolean available = pythonYfinanceService.isAvailable();
        logger.info("Python yfinance available: {}", available);
        
        if (!available) {
            logger.warn("Python yfinance not available - skipping integration test");
            return;
        }
        
        assertTrue(available, "Python yfinance should be available");
    }

    @Test
    void testGetStockCandles() {
        logger.info("Testing stock candle fetching...");
        
        if (!pythonYfinanceService.isAvailable()) {
            logger.warn("Python yfinance not available - skipping stock candle test");
            return;
        }

        // Test with AAPL for January 2024
        String symbol = "AAPL";
        LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
        LocalDateTime endDate = LocalDateTime.of(2024, 1, 31, 0, 0);
        
        long startTimestamp = startDate.toEpochSecond(ZoneOffset.UTC);
        long endTimestamp = endDate.toEpochSecond(ZoneOffset.UTC);
        
        logger.info("Fetching {} data from {} to {}", symbol, startDate.toLocalDate(), endDate.toLocalDate());
        
        List<YahooStockCandle> candles = pythonYfinanceService.getStockCandles(symbol, startTimestamp, endTimestamp);
        
        assertNotNull(candles, "Candles should not be null");
        assertFalse(candles.isEmpty(), "Should have received some candles");
        
        logger.info("Received {} candles", candles.size());
        
        // Verify first candle
        YahooStockCandle firstCandle = candles.get(0);
        assertEquals(symbol, firstCandle.getSymbol());
        assertNotNull(firstCandle.getTimestamp());
        assertNotNull(firstCandle.getOpen());
        assertNotNull(firstCandle.getHigh());
        assertNotNull(firstCandle.getLow());
        assertNotNull(firstCandle.getClose());
        assertNotNull(firstCandle.getAdjClose());
        assertTrue(firstCandle.getVolume() > 0);
        
        logger.info("First candle: {} - Open: {}, High: {}, Low: {}, Close: {}, Volume: {}", 
                   firstCandle.getTimestamp().toLocalDate(),
                   firstCandle.getOpen(),
                   firstCandle.getHigh(),
                   firstCandle.getLow(),
                   firstCandle.getClose(),
                   firstCandle.getVolume());
        
        // Verify data integrity
        for (YahooStockCandle candle : candles) {
            assertTrue(candle.getHigh().compareTo(candle.getLow()) >= 0, "High should be >= Low");
            assertTrue(candle.getHigh().compareTo(candle.getOpen()) >= 0, "High should be >= Open");
            assertTrue(candle.getHigh().compareTo(candle.getClose()) >= 0, "High should be >= Close");
            assertTrue(candle.getLow().compareTo(candle.getOpen()) <= 0, "Low should be <= Open");
            assertTrue(candle.getLow().compareTo(candle.getClose()) <= 0, "Low should be <= Close");
        }
        
        logger.info("All candles passed data integrity checks");
    }

    @Test
    void testGetStockCandlesWithInvalidSymbol() {
        if (!pythonYfinanceService.isAvailable()) {
            logger.warn("Python yfinance not available - skipping invalid symbol test");
            return;
        }

        logger.info("Testing with invalid symbol...");
        
        String invalidSymbol = "INVALID_SYMBOL_12345";
        LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
        LocalDateTime endDate = LocalDateTime.of(2024, 1, 31, 0, 0);
        
        long startTimestamp = startDate.toEpochSecond(ZoneOffset.UTC);
        long endTimestamp = endDate.toEpochSecond(ZoneOffset.UTC);
        
        List<YahooStockCandle> candles = pythonYfinanceService.getStockCandles(invalidSymbol, startTimestamp, endTimestamp);
        
        assertNotNull(candles, "Candles should not be null even for invalid symbol");
        assertTrue(candles.isEmpty(), "Should have no candles for invalid symbol");
        
        logger.info("Invalid symbol correctly returned empty result");
    }
}
