package com.trading.financialindicatordaemon.service.yahoo;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test to verify the complete Yahoo Finance service with Python fallback
 */
class YahooFinanceIntegrationTest {

    private static final Logger logger = LoggerFactory.getLogger(YahooFinanceIntegrationTest.class);
    
    @Test
    void testPythonYfinanceIntegration() {
        logger.info("Testing Python yfinance integration...");
        
        PythonYfinanceService pythonService = new PythonYfinanceService();
        
        // Check if Python yfinance is available
        boolean available = pythonService.isAvailable();
        logger.info("Python yfinance available: {}", available);
        
        if (!available) {
            logger.warn("Python yfinance not available - test will be skipped");
            logger.info("To enable this test, run: ./scripts/setup_python_env.sh");
            return;
        }
        
        // Test fetching data for multiple symbols
        String[] symbols = {"AAPL", "TSLA", "MSFT"};
        LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
        LocalDateTime endDate = LocalDateTime.of(2024, 1, 10, 0, 0);
        
        long startTimestamp = startDate.toEpochSecond(ZoneOffset.UTC);
        long endTimestamp = endDate.toEpochSecond(ZoneOffset.UTC);
        
        for (String symbol : symbols) {
            logger.info("Testing symbol: {}", symbol);
            
            List<YahooStockCandle> candles = pythonService.getStockCandles(symbol, startTimestamp, endTimestamp);
            
            assertNotNull(candles, "Candles should not be null for " + symbol);
            assertFalse(candles.isEmpty(), "Should have received candles for " + symbol);
            
            logger.info("Symbol {} returned {} candles", symbol, candles.size());
            
            // Verify data quality
            YahooStockCandle firstCandle = candles.get(0);
            assertEquals(symbol, firstCandle.getSymbol());
            assertTrue(firstCandle.getOpen().doubleValue() > 0, "Open price should be positive");
            assertTrue(firstCandle.getVolume() > 0, "Volume should be positive");
            
            logger.info("Symbol {} - First candle: {} Open: ${} Volume: {}", 
                       symbol, 
                       firstCandle.getTimestamp().toLocalDate(),
                       firstCandle.getOpen(),
                       firstCandle.getVolume());
        }
        
        logger.info("✅ All symbols fetched successfully - Python yfinance is working!");
    }
    
    @Test
    void testRateLimitingBehavior() {
        logger.info("Testing rate limiting behavior...");
        
        PythonYfinanceService pythonService = new PythonYfinanceService();
        
        if (!pythonService.isAvailable()) {
            logger.warn("Python yfinance not available - skipping rate limiting test");
            return;
        }
        
        // Test multiple rapid requests to see if we get rate limited
        String[] symbols = {"AAPL", "MSFT", "GOOGL", "TSLA", "AMZN"};
        LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
        LocalDateTime endDate = LocalDateTime.of(2024, 1, 5, 0, 0);
        
        long startTimestamp = startDate.toEpochSecond(ZoneOffset.UTC);
        long endTimestamp = endDate.toEpochSecond(ZoneOffset.UTC);
        
        int successCount = 0;
        int failureCount = 0;
        
        for (String symbol : symbols) {
            logger.info("Rapid request for symbol: {}", symbol);
            
            List<YahooStockCandle> candles = pythonService.getStockCandles(symbol, startTimestamp, endTimestamp);
            
            if (!candles.isEmpty()) {
                successCount++;
                logger.info("✅ {} - Success ({} candles)", symbol, candles.size());
            } else {
                failureCount++;
                logger.warn("❌ {} - Failed (empty result)", symbol);
            }
        }
        
        logger.info("Rate limiting test results: {} successes, {} failures", successCount, failureCount);
        
        // We expect most requests to succeed with Python yfinance
        assertTrue(successCount >= 3, "Should have at least 3 successful requests out of 5");
        
        if (failureCount == 0) {
            logger.info("🎉 No rate limiting detected - Python yfinance is working perfectly!");
        } else {
            logger.info("⚠️ Some rate limiting detected, but {} out of {} requests succeeded", 
                       successCount, symbols.length);
        }
    }
}
