package com.trading.financialindicatordaemon.service.stock;

import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.mapper.StockCandleDataSymbolAndConversionCurrency;
import com.trading.financialindicatordaemon.service.yahoo.YahooFinanceService;
import com.trading.financialindicatordaemon.service.yahoo.YahooStockCandle;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

public class StockCandleDataServiceTest extends BaseTest {

    @Autowired
    private StockCandleDataService stockCandleDataService;
    @Autowired
    private YahooFinanceService yahooFinanceService;


    @Test
    public void insert_shouldStore() {
        List<YahooStockCandle> candles = yahooFinanceService.getStockCandles("TSLA", 1735430400, 1751155200);
        assertThat(candles).isNotEmpty();
        assertThat(candles).hasSize(123);

        stockCandleDataService.insertYahoo(candles);

        List<StockCandleData> stockCandleData = stockCandleDataService.find("TSLA", "USD");
        assertThat(stockCandleData).isNotEmpty();
        assertThat(stockCandleData).hasSize(123);
    }

    @Test
    public void findAllSymbolAndConversionCurrency_shouldReturnSymbols() {
        List<YahooStockCandle> candles = yahooFinanceService.getStockCandles("TSLA", 1735430400, 1751155200);
        stockCandleDataService.insertYahoo(candles);

        List<StockCandleDataSymbolAndConversionCurrency> symbolsAndCurrencies =
                stockCandleDataService.findAllSymbolAndConversionCurrency();
        assertThat(symbolsAndCurrencies).isNotEmpty();
        assertThat(symbolsAndCurrencies).hasSize(1);
        assertThat(symbolsAndCurrencies.getFirst().getSymbol()).isEqualTo("TSLA");
        assertThat(symbolsAndCurrencies.getFirst().getConversionCurrency()).isEqualTo("USD");
    }

    @Test
    public void findLatestQuoteForAllSymbolsAndConversionCurrencies_shouldReturnLatestQuotes() {
        List<YahooStockCandle> candles = yahooFinanceService.getStockCandles("TSLA", 1735430400, 1751155200);
        stockCandleDataService.insertYahoo(candles);

        List<StockCandleData> latestQuotes =
                stockCandleDataService.findLatestQuoteForAllSymbolsAndConversionCurrencies();
        assertThat(latestQuotes).isNotEmpty();
        assertThat(latestQuotes).hasSize(1);
        assertThat(latestQuotes.getFirst().getSymbol()).isEqualTo("TSLA");
    }

}
