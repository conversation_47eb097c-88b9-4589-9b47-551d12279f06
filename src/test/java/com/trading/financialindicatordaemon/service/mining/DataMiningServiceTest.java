package com.trading.financialindicatordaemon.service.mining;

import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.client.cmc.CryptoCandleHistoricalQuote;
import com.trading.financialindicatordaemon.service.cmc.CmcCandleDataService;
import com.trading.financialindicatordaemon.service.cmc.CmcMappingsMappingService;
import com.trading.financialindicatordaemon.service.stock.StockCandleData;
import com.trading.financialindicatordaemon.service.stock.StockCandleDataService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static com.trading.financialindicatordaemon.config.AppConfig.USD_CURRENCY_ID;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class DataMiningServiceTest extends BaseTest {

    @Autowired
    private DataMiningService dataMiningService;
    @Autowired
    private CmcMappingsMappingService cmcMappingsMappingService;
    @Autowired
    private CmcCandleDataService cmcCandleDataService;
    @Autowired
    private StockCandleDataService stockCandleDataService;

    @Test
    public void findMappings_shouldStore() {
        assertTrue(cmcMappingsMappingService.findMapping("BTC").isEmpty());
        dataMiningService.mineMappings();
        assertTrue(cmcMappingsMappingService.findMapping("BTC").isPresent());
    }

    // mineSymbols
    @Test
    public void mineSymbols_shouldStore() {
        dataMiningService.mineMappings();
        dataMiningService.mineSymbols(List.of("SOL"), USD_CURRENCY_ID);
        List<CryptoCandleHistoricalQuote> cryptoCandleHistoricalQuotes = cmcCandleDataService.find("SOL", "USD");
        assertThat(cryptoCandleHistoricalQuotes).isNotEmpty();
        assertThat(cryptoCandleHistoricalQuotes).hasSize(274);
    }

    @Test
    public void mineStockSymbols_shouldStore() {
        dataMiningService.mineStockSymbols(List.of("TSLA"));
        List<StockCandleData> stockCandleData = stockCandleDataService.find("TSLA", "USD");
        assertThat(stockCandleData).isNotEmpty();
        assertThat(stockCandleData).hasSize(1);
    }

}
