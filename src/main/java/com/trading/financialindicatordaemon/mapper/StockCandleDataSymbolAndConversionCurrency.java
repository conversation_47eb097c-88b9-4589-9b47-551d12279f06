package com.trading.financialindicatordaemon.mapper;

public class StockCandleDataSymbolAndConversionCurrency {
    private String symbol;
    private String conversionCurrency;

    public StockCandleDataSymbolAndConversionCurrency() {
    }

    public StockCandleDataSymbolAndConversionCurrency(String symbol, String conversionCurrency) {
        this.symbol = symbol;
        this.conversionCurrency = conversionCurrency;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public String getConversionCurrency() {
        return conversionCurrency;
    }

    public void setConversionCurrency(String conversionCurrency) {
        this.conversionCurrency = conversionCurrency;
    }

    @Override
    public String toString() {
        return "StockCandleDataSymbolAndConversionCurrency{" +
                "symbol='" + symbol + '\'' +
                ", conversionCurrency='" + conversionCurrency + '\'' +
                '}';
    }
}
