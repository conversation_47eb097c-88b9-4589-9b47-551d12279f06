package com.trading.financialindicatordaemon.service.mining;

import com.trading.financialindicatordaemon.client.cmc.CryptoCandleHistoricalQuote;
import com.trading.financialindicatordaemon.client.cmc.CryptoCandleHistoricalQuotes;
import com.trading.financialindicatordaemon.client.cmc.CryptocurrencyMapping;
import com.trading.financialindicatordaemon.config.AppConfig;
import com.trading.financialindicatordaemon.service.UnixTimestampService;
import com.trading.financialindicatordaemon.service.cmc.CmcCandleDataService;
import com.trading.financialindicatordaemon.service.cmc.CmcMappingsMappingService;
import com.trading.financialindicatordaemon.service.cmc.CoinMarketCapService;
import com.trading.financialindicatordaemon.service.stock.StockCandleDataService;
import com.trading.financialindicatordaemon.service.yahoo.YahooFinanceService;
import com.trading.financialindicatordaemon.service.yahoo.YahooStockCandle;
import com.trading.financialindicatordaemon.util.TimeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class DataMiningService {

    private static final Logger logger = LoggerFactory.getLogger(DataMiningService.class);

    private final CoinMarketCapService coinMarketCapService;
    private final CmcMappingsMappingService cmcMappingsMappingService;
    private final UnixTimestampService unixTimestampService;
    private final CmcCandleDataService cmcCandleDataService;
    private final YahooFinanceService yahooFinanceService;
    private final StockCandleDataService stockCandleDataService;

    public DataMiningService(CoinMarketCapService coinMarketCapService,
                           CmcMappingsMappingService cmcMappingsMappingService,
                           UnixTimestampService unixTimestampService,
                           CmcCandleDataService cmcCandleDataService,
                           YahooFinanceService yahooFinanceService,
                           StockCandleDataService stockCandleDataService) {
        this.coinMarketCapService = coinMarketCapService;
        this.cmcMappingsMappingService = cmcMappingsMappingService;
        this.unixTimestampService = unixTimestampService;
        this.cmcCandleDataService = cmcCandleDataService;
        this.yahooFinanceService = yahooFinanceService;
        this.stockCandleDataService = stockCandleDataService;
    }

    public void mineMappings() {
        logger.info("Starting cryptocurrency mappings retrieval");
        if (cmcMappingsMappingService.doMappingsExist()) {
            logger.info("Mappings already exist, skipping download");
            return;
        }

        List<CryptocurrencyMapping> mappings = coinMarketCapService.getMappings();
        cmcMappingsMappingService.insert(mappings);
        logger.info("Completed cryptocurrency mappings retrieval");
    }

    public void mineSymbols(List<String> symbols, Integer currencyId) {
        logger.info("Starting data mining for symbols: {}", symbols);

        if (!cmcMappingsMappingService.doMappingsExist()) {
            logger.error("Mappings not found, cannot mine symbols");
            throw new RuntimeException("Mappings not found");
        }

        String conversionCurrency = switch (currencyId) {
            case AppConfig.BTC_CURRENCY_ID -> "BTC";
            case AppConfig.USD_CURRENCY_ID -> "USD";
            default -> throw new RuntimeException("Unsupported currency ID: " + currencyId);
        };

        symbols.stream()
                .map(cmcMappingsMappingService::findMapping)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .forEach(mapping -> cmcCandleDataService.insert(
                        mapping.symbol(),
                        conversionCurrency,
                        fetchAllHistoricalQuotes(mapping.symbol(), conversionCurrency, mapping.id(), currencyId)
                ));
    }

    private List<CryptoCandleHistoricalQuote> fetchAllHistoricalQuotes(String symbol, String conversionCurrency, Integer mappingId, Integer currencyId) {
        List<CryptoCandleHistoricalQuote> allQuotes = new ArrayList<>();
        long timeEnd = unixTimestampService.getCurrentUnixTimestamp();

        Optional<Long> latestCloseTimestamp =
                cmcCandleDataService.findLatestCloseTimestamp(symbol, conversionCurrency)
                        .map(TimeUtils::convertToUnixTimestamp);

        if (latestCloseTimestamp.isPresent() && timeEnd - latestCloseTimestamp.get() < 86400) {
            logger.info("Latest close timestamp for {} is within 24h, skipping", symbol);
            return allQuotes;
        }

        if (latestCloseTimestamp.isPresent() && timeEnd - latestCloseTimestamp.get() < 15552000) {
            logger.info("Fetching historical quotes for mapping ID: {} from {} to {}", mappingId, latestCloseTimestamp.get(), timeEnd);
            return coinMarketCapService.findQuotes(mappingId, currencyId, String.valueOf(latestCloseTimestamp.get()), String.valueOf(timeEnd)).getQuotes();
        }

        while (true) {
            logger.info("Fetching historical quotes for mapping ID: {} from {} to {}", mappingId, timeEnd - 15552000, timeEnd);

            String timeStart = String.valueOf(timeEnd - 15552000);

            CryptoCandleHistoricalQuotes historicalQuotes = coinMarketCapService
                    .findQuotes(mappingId, currencyId, timeStart, String.valueOf(timeEnd));

            List<CryptoCandleHistoricalQuote> quotes = historicalQuotes.getQuotes();
            if (quotes.isEmpty()) {
                logger.info("No more historical quotes found for mapping ID: {}", mappingId);
                break;
            }

            allQuotes.addAll(quotes);
            timeEnd -= 15552000;
        }

        return allQuotes;
    }

    public void mineStockSymbols(List<String> symbols) {
        logger.info("Starting stock data mining for symbols: {}", symbols);

        for (String symbol : symbols) {
            try {
                mineStockSymbol(symbol, "USD");
            } catch (Exception e) {
                logger.error("Failed to mine stock data for symbol: {}", symbol, e);
            }
        }
    }

    private void mineStockSymbol(String symbol, String conversionCurrency) {
        logger.info("Mining stock data for symbol: {} in currency: {}", symbol, conversionCurrency);

        // Check if we have recent data (within 24 hours)
        Optional<LocalDate> latestTimestamp = stockCandleDataService.findLatestTimestamp(symbol, conversionCurrency);
        long currentUnixTimestamp = unixTimestampService.getCurrentUnixTimestamp();

        LocalDate end = TimeUtils.convertToLocalDateTime(currentUnixTimestamp).toLocalDate();

        if (latestTimestamp.isPresent() && latestTimestamp.get().equals(end)) {
            logger.info("Latest data for {} is current ({}), skipping", symbol, latestTimestamp.get());
            return;
        }

        // Calculate time range for fetching data
        long endTimestamp = end.atStartOfDay().toEpochSecond(ZoneOffset.UTC);
        long startTimestamp;

        if (latestTimestamp.isPresent()) {
            // Fetch from last known date
            startTimestamp = latestTimestamp.get().atStartOfDay().toEpochSecond(ZoneOffset.UTC);
            logger.info("Fetching stock data for {} from {} to {}", symbol, latestTimestamp.get(), end);
        } else {
            // Fetch last 180 days for new symbols
            startTimestamp = end.minusDays(180).atStartOfDay().toEpochSecond(ZoneOffset.UTC);
            logger.info("Fetching initial stock data for {} (last 180 days)", symbol);
        }

        // Fetch and store stock data
        List<YahooStockCandle> candles = yahooFinanceService.getStockCandles(symbol, startTimestamp, endTimestamp);
        if (!candles.isEmpty()) {
            stockCandleDataService.insertYahoo(candles);
            logger.info("Successfully stored {} candles for stock symbol: {}", candles.size(), symbol);
        } else {
            logger.warn("No stock data found for symbol: {}", symbol);
        }
    }

}
