package com.trading.financialindicatordaemon.service.yahoo;

import com.trading.financialindicatordaemon.client.yahoo.YahooFinanceApiClient;
import com.trading.financialindicatordaemon.client.yahoo.YahooFinanceResponse;
import com.trading.financialindicatordaemon.client.yahoo.YahooIndicators;
import com.trading.financialindicatordaemon.config.AppConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@Service
public class YahooFinanceService {

    private static final Logger logger = LoggerFactory.getLogger(YahooFinanceService.class);

    private final YahooFinanceApiClient yahooFinanceApiClient;
    private final AppConfig appConfig;
    private final Random random = new Random();

    public YahooFinanceService(YahooFinanceApiClient yahooFinanceApiClient, AppConfig appConfig) {
        this.yahooFinanceApiClient = yahooFinanceApiClient;
        this.appConfig = appConfig;
    }

    /**
     * Fetch stock candle data for a given symbol and time period
     */
    public List<YahooStockCandle> getStockCandles(String symbol, long startTimestamp, long endTimestamp) {
        logger.info("Fetching stock data for symbol: {} from {} to {}", symbol, startTimestamp, endTimestamp);

        throttleRequest();

        // Try Python yfinance first (more reliable due to curl_cffi)
        List<YahooStockCandle> pythonResult = fetchWithPythonYfinance(symbol, startTimestamp, endTimestamp);
        if (!pythonResult.isEmpty()) {
            return pythonResult;
        }

        // Fallback to Java Feign client
        logger.warn("Python yfinance failed, falling back to Java client for symbol: {}", symbol);
        ResponseEntity<YahooFinanceResponse> response = yahooFinanceApiClient.getStockChart(
                symbol,
                startTimestamp,
                endTimestamp,
                "1d", // Daily interval
                true, // Include pre/post market data
                "div|split|earn", // Include dividend, split, and earnings events
                "en-US",
                "US",
                "cosaic"
        );

        if (!response.getStatusCode().is2xxSuccessful()) {
            logger.error("Failed to fetch stock data for {}: HTTP {}", symbol, response.getStatusCode());
            return List.of();
        }

        return parseYahooFinanceResponse(symbol, response.getBody());
    }

    /**
     * Fetch data using Python yfinance (more reliable due to curl_cffi)
     */
    private List<YahooStockCandle> fetchWithPythonYfinance(String symbol, long startTimestamp, long endTimestamp) {
        try {
            // Convert timestamps to dates for Python script
            String startDate = Instant.ofEpochSecond(startTimestamp).toString().substring(0, 10);
            String endDate = Instant.ofEpochSecond(endTimestamp).toString().substring(0, 10);

            ProcessBuilder pb = new ProcessBuilder(
                "python3", "-c",
                String.format("""
                    import yfinance as yf
                    import json
                    import sys

                    try:
                        ticker = yf.Ticker('%s')
                        data = ticker.history(start='%s', end='%s', interval='1d')

                        result = []
                        for date, row in data.iterrows():
                            candle = {
                                'symbol': '%s',
                                'timestamp': int(date.timestamp()),
                                'open': float(row['Open']),
                                'high': float(row['High']),
                                'low': float(row['Low']),
                                'close': float(row['Close']),
                                'adjClose': float(row['Close']),
                                'volume': int(row['Volume']),
                                'currency': 'USD'
                            }
                            result.append(candle)

                        print(json.dumps(result))
                    except Exception as e:
                        print(json.dumps({'error': str(e)}), file=sys.stderr)
                        sys.exit(1)
                    """, symbol, startDate, endDate, symbol)
            );

            Process process = pb.start();

            // Read output
            String output = new String(process.getInputStream().readAllBytes());
            String error = new String(process.getErrorStream().readAllBytes());

            int exitCode = process.waitFor();

            if (exitCode == 0 && !output.trim().isEmpty()) {
                return parsePythonYfinanceOutput(output);
            } else {
                logger.warn("Python yfinance failed for {}: exit={}, error={}", symbol, exitCode, error);
                return List.of();
            }

        } catch (Exception e) {
            logger.error("Error executing Python yfinance for symbol {}: {}", symbol, e.getMessage());
            return List.of();
        }
    }

    /**
     * Parse Yahoo Finance API response into StockCandle objects
     */
    private List<YahooStockCandle> parseYahooFinanceResponse(String symbol, YahooFinanceResponse response) {
        List<YahooStockCandle> candles = new ArrayList<>();

        if (response == null || response.getChart() == null ||
                response.getChart().getResult() == null || response.getChart().getResult().length == 0) {
            logger.warn("Empty or invalid response for symbol: {}", symbol);
            return candles;
        }

        YahooFinanceResponse.Result result = response.getChart().getResult()[0];

        if (result.getTimestamp() == null || result.getIndicators() == null ||
                result.getIndicators().getQuote() == null || result.getIndicators().getQuote().length == 0) {
            logger.warn("Missing timestamp or quote data for symbol: {}", symbol);
            return candles;
        }

        long[] timestamps = result.getTimestamp();
        YahooIndicators.Quote quote = result.getIndicators().getQuote()[0];

        // Get adjusted close prices if available
        List<BigDecimal> adjCloses = null;
        if (result.getIndicators().getAdjclose() != null && result.getIndicators().getAdjclose().length > 0) {
            adjCloses = result.getIndicators().getAdjclose()[0].getAdjclose();
        }

        // Validate array lengths
        if (timestamps.length != quote.getOpen().size() ||
                timestamps.length != quote.getHigh().size() ||
                timestamps.length != quote.getLow().size() ||
                timestamps.length != quote.getClose().size() ||
                timestamps.length != quote.getVolume().length) {
            logger.error("Mismatched array lengths in Yahoo Finance response for symbol: {}", symbol);
            return candles;
        }

        // Convert arrays to StockCandle objects
        for (int i = 0; i < timestamps.length; i++) {
            try {
                LocalDateTime timestamp = LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(timestamps[i]), ZoneOffset.UTC);

                BigDecimal adjClose = (adjCloses != null && i < adjCloses.size()) ?
                        adjCloses.get(i) : quote.getClose().get(i);

                YahooStockCandle candle = new YahooStockCandle(
                        symbol,
                        timestamp,
                        quote.getOpen().get(i),
                        quote.getHigh().get(i),
                        quote.getLow().get(i),
                        quote.getClose().get(i),
                        adjClose,
                        quote.getVolume()[i],
                        result.getMeta().getCurrency()
                );

                candles.add(candle);

            } catch (Exception e) {
                logger.warn("Error parsing candle data at index {} for symbol {}: {}", i, symbol, e.getMessage());
            }
        }

        logger.info("Successfully parsed {} candles for symbol: {}", candles.size(), symbol);
        return candles;
    }

    /**
     * Add throttling to avoid rate limiting (similar to CMC implementation)
     */
    private void throttleRequest() {
        try {
            // Use default values if yahoo config is not available
            int min = (appConfig.yahoo() != null && appConfig.yahoo().throttle() != null) ?
                    appConfig.yahoo().throttle().min() : 300;
            int max = (appConfig.yahoo() != null && appConfig.yahoo().throttle() != null) ?
                    appConfig.yahoo().throttle().max() : 500;
            int delay = random.nextInt(max - min + 1) + min;
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Throttling interrupted", e);
        }
    }

}
