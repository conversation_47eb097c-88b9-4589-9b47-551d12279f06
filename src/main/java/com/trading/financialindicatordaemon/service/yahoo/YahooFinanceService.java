package com.trading.financialindicatordaemon.service.yahoo;

import com.trading.financialindicatordaemon.client.yahoo.YahooFinanceResponse;
import com.trading.financialindicatordaemon.client.yahoo.YahooIndicators;
import com.trading.financialindicatordaemon.config.AppConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@Service
public class YahooFinanceService {

    private static final Logger logger = LoggerFactory.getLogger(YahooFinanceService.class);

    private final AppConfig appConfig;
    private final Random random = new Random();
    private final PythonYfinanceService pythonYfinanceService;

    public YahooFinanceService(AppConfig appConfig,
                               PythonYfinanceService pythonYfinanceService) {
        this.appConfig = appConfig;
        this.pythonYfinanceService = pythonYfinanceService;
    }

    /**
     * Fetch stock candle data for a given symbol and time period
     */
    public List<YahooStockCandle> getStockCandles(String symbol, long startTimestamp, long endTimestamp) {
        logger.info("Fetching stock data for symbol: {} from {} to {}", symbol, startTimestamp, endTimestamp);

        throttleRequest();

        return pythonYfinanceService.getStockCandles(symbol, startTimestamp, endTimestamp);
    }

    /**
     * Add throttling to avoid rate limiting (similar to CMC implementation)
     */
    private void throttleRequest() {
        try {
            // Use default values if yahoo config is not available
            int min = (appConfig.yahoo() != null && appConfig.yahoo().throttle() != null) ?
                    appConfig.yahoo().throttle().min() : 300;
            int max = (appConfig.yahoo() != null && appConfig.yahoo().throttle() != null) ?
                    appConfig.yahoo().throttle().max() : 500;
            int delay = random.nextInt(max - min + 1) + min;
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Throttling interrupted", e);
        }
    }

}
