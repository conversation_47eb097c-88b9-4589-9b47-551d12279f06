package com.trading.financialindicatordaemon.service.yahoo;

import com.trading.financialindicatordaemon.client.yahoo.YahooFinanceApiClient;
import com.trading.financialindicatordaemon.client.yahoo.YahooFinanceResponse;
import com.trading.financialindicatordaemon.client.yahoo.YahooIndicators;
import com.trading.financialindicatordaemon.config.AppConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@Service
public class YahooFinanceService {

    private static final Logger logger = LoggerFactory.getLogger(YahooFinanceService.class);

    private final YahooFinanceApiClient yahooFinanceApiClient;
    private final AppConfig appConfig;
    private final Random random = new Random();
    private final RestTemplate restTemplate = new RestTemplate();

    // Session management
    private final ConcurrentHashMap<String, String> sessionCookies = new ConcurrentHashMap<>();
    private volatile String crumbToken = null;
    private final AtomicLong lastCrumbUpdate = new AtomicLong(0);
    private static final long CRUMB_EXPIRY_MS = 30 * 60 * 1000; // 30 minutes

    public YahooFinanceService(YahooFinanceApiClient yahooFinanceApiClient, AppConfig appConfig) {
        this.yahooFinanceApiClient = yahooFinanceApiClient;
        this.appConfig = appConfig;
    }

    /**
     * Get or refresh the Yahoo Finance session and crumb token
     */
    private void ensureValidSession() {
        long now = System.currentTimeMillis();
        if (crumbToken == null || (now - lastCrumbUpdate.get()) > CRUMB_EXPIRY_MS) {
            refreshSession();
        }
    }

    /**
     * Refresh Yahoo Finance session by getting new cookies and crumb
     */
    private void refreshSession() {
        try {
            logger.info("Refreshing Yahoo Finance session and crumb token");

            // Step 1: Get session cookies by visiting Yahoo Finance homepage
            HttpHeaders headers = createBrowserHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<String> homeResponse = restTemplate.exchange(
                "https://finance.yahoo.com/",
                HttpMethod.GET,
                entity,
                String.class
            );

            // Extract cookies from response
            List<String> setCookieHeaders = homeResponse.getHeaders().get("Set-Cookie");
            if (setCookieHeaders != null) {
                sessionCookies.clear();
                for (String cookieHeader : setCookieHeaders) {
                    String[] cookieParts = cookieHeader.split(";")[0].split("=", 2);
                    if (cookieParts.length == 2) {
                        sessionCookies.put(cookieParts[0], cookieParts[1]);
                    }
                }
                logger.debug("Extracted {} session cookies", sessionCookies.size());
            }

            // Step 2: Get crumb token using the session cookies
            headers = createBrowserHeaders();
            headers.set("Cookie", buildCookieString());
            entity = new HttpEntity<>(headers);

            ResponseEntity<String> crumbResponse = restTemplate.exchange(
                "https://query2.finance.yahoo.com/v1/test/getcrumb",
                HttpMethod.GET,
                entity,
                String.class
            );

            if (crumbResponse.getStatusCode().is2xxSuccessful() && crumbResponse.getBody() != null) {
                crumbToken = crumbResponse.getBody().trim();
                lastCrumbUpdate.set(System.currentTimeMillis());
                logger.info("Successfully refreshed crumb token: {}", crumbToken.substring(0, Math.min(8, crumbToken.length())) + "...");
            } else {
                logger.error("Failed to get crumb token: HTTP {}", crumbResponse.getStatusCode());
                throw new RuntimeException("Failed to get Yahoo Finance crumb token");
            }

        } catch (Exception e) {
            logger.error("Failed to refresh Yahoo Finance session", e);
            throw new RuntimeException("Failed to refresh Yahoo Finance session", e);
        }
    }

    /**
     * Create realistic browser headers for session establishment
     */
    private HttpHeaders createBrowserHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("User-Agent",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.set("Accept",
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        headers.set("Accept-Language", "en-US,en;q=0.9");
        headers.set("Accept-Encoding", "gzip, deflate, br");
        headers.set("DNT", "1");
        headers.set("Connection", "keep-alive");
        headers.set("Upgrade-Insecure-Requests", "1");
        headers.set("Sec-Fetch-Dest", "document");
        headers.set("Sec-Fetch-Mode", "navigate");
        headers.set("Sec-Fetch-Site", "none");
        headers.set("Sec-Fetch-User", "?1");
        headers.set("Cache-Control", "max-age=0");
        return headers;
    }

    /**
     * Build cookie string from session cookies
     */
    private String buildCookieString() {
        return sessionCookies.entrySet().stream()
            .map(entry -> entry.getKey() + "=" + entry.getValue())
            .reduce((a, b) -> a + "; " + b)
            .orElse("");
    }

    /**
     * Fetch stock candle data for a given symbol and time period with retry logic
     */
    public List<YahooStockCandle> getStockCandles(String symbol, long startTimestamp, long endTimestamp) {
        logger.info("Fetching stock data for symbol: {} from {} to {}", symbol, startTimestamp, endTimestamp);

        int maxRetries = 3;
        int retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                // Ensure we have a valid session and crumb
                ensureValidSession();

                throttleRequest();

                ResponseEntity<YahooFinanceResponse> response = yahooFinanceApiClient.getStockChart(
                        symbol,
                        startTimestamp,
                        endTimestamp,
                        "1d", // Daily interval
                        true, // Include pre/post market data
                        "div|split|earn", // Include dividend, split, and earnings events
                        "en-US",
                        "US",
                        "cosaic",
                        crumbToken,
                        buildCookieString()
                );

                if (response.getStatusCode().is2xxSuccessful()) {
                    return parseYahooFinanceResponse(symbol, response.getBody());
                } else if (response.getStatusCode() == HttpStatus.TOO_MANY_REQUESTS) {
                    logger.warn("Rate limited for symbol {}, attempt {} of {}", symbol, retryCount + 1, maxRetries);
                    retryCount++;
                    if (retryCount < maxRetries) {
                        // Exponential backoff for rate limiting
                        long backoffDelay = (long) (Math.pow(2, retryCount) * 5000); // 5s, 10s, 20s
                        logger.info("Backing off for {} ms before retry", backoffDelay);
                        Thread.sleep(backoffDelay);
                    }
                } else if (response.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                    logger.warn("Unauthorized response, refreshing session for symbol {}, attempt {} of {}", symbol, retryCount + 1, maxRetries);
                    // Force session refresh on 401
                    crumbToken = null;
                    retryCount++;
                    if (retryCount < maxRetries) {
                        Thread.sleep(1000); // Brief pause before retry
                    }
                } else {
                    logger.error("Failed to fetch stock data for {}: HTTP {}", symbol, response.getStatusCode());
                    return List.of();
                }
            } catch (HttpClientErrorException e) {
                if (e.getStatusCode() == HttpStatus.TOO_MANY_REQUESTS) {
                    logger.warn("Rate limited for symbol {}, attempt {} of {}", symbol, retryCount + 1, maxRetries);
                    retryCount++;
                    if (retryCount < maxRetries) {
                        try {
                            // Exponential backoff for rate limiting
                            long backoffDelay = (long) (Math.pow(2, retryCount) * 5000); // 5s, 10s, 20s
                            logger.info("Backing off for {} ms before retry", backoffDelay);
                            Thread.sleep(backoffDelay);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            logger.error("Interrupted during backoff", ie);
                            return List.of();
                        }
                    }
                } else if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                    logger.warn("Unauthorized error, refreshing session for symbol {}, attempt {} of {}", symbol, retryCount + 1, maxRetries);
                    // Force session refresh on 401
                    crumbToken = null;
                    retryCount++;
                    if (retryCount < maxRetries) {
                        try {
                            Thread.sleep(1000); // Brief pause before retry
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            logger.error("Interrupted during retry pause", ie);
                            return List.of();
                        }
                    }
                } else {
                    logger.error("HTTP error fetching stock data for {}: {}", symbol, e.getMessage());
                    return List.of();
                }
            } catch (Exception e) {
                logger.error("Unexpected error fetching stock data for {}: {}", symbol, e.getMessage());
                return List.of();
            }
        }

        logger.error("Failed to fetch stock data for {} after {} retries", symbol, maxRetries);
        return List.of();
    }

    /**
     * Parse Yahoo Finance API response into StockCandle objects
     */
    private List<YahooStockCandle> parseYahooFinanceResponse(String symbol, YahooFinanceResponse response) {
        List<YahooStockCandle> candles = new ArrayList<>();

        if (response == null || response.getChart() == null ||
                response.getChart().getResult() == null || response.getChart().getResult().length == 0) {
            logger.warn("Empty or invalid response for symbol: {}", symbol);
            return candles;
        }

        YahooFinanceResponse.Result result = response.getChart().getResult()[0];

        if (result.getTimestamp() == null || result.getIndicators() == null ||
                result.getIndicators().getQuote() == null || result.getIndicators().getQuote().length == 0) {
            logger.warn("Missing timestamp or quote data for symbol: {}", symbol);
            return candles;
        }

        long[] timestamps = result.getTimestamp();
        YahooIndicators.Quote quote = result.getIndicators().getQuote()[0];

        // Get adjusted close prices if available
        List<BigDecimal> adjCloses = null;
        if (result.getIndicators().getAdjclose() != null && result.getIndicators().getAdjclose().length > 0) {
            adjCloses = result.getIndicators().getAdjclose()[0].getAdjclose();
        }

        // Validate array lengths
        if (timestamps.length != quote.getOpen().size() ||
                timestamps.length != quote.getHigh().size() ||
                timestamps.length != quote.getLow().size() ||
                timestamps.length != quote.getClose().size() ||
                timestamps.length != quote.getVolume().length) {
            logger.error("Mismatched array lengths in Yahoo Finance response for symbol: {}", symbol);
            return candles;
        }

        // Convert arrays to StockCandle objects
        for (int i = 0; i < timestamps.length; i++) {
            try {
                LocalDateTime timestamp = LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(timestamps[i]), ZoneOffset.UTC);

                BigDecimal adjClose = (adjCloses != null && i < adjCloses.size()) ?
                        adjCloses.get(i) : quote.getClose().get(i);

                YahooStockCandle candle = new YahooStockCandle(
                        symbol,
                        timestamp,
                        quote.getOpen().get(i),
                        quote.getHigh().get(i),
                        quote.getLow().get(i),
                        quote.getClose().get(i),
                        adjClose,
                        quote.getVolume()[i],
                        result.getMeta().getCurrency()
                );

                candles.add(candle);

            } catch (Exception e) {
                logger.warn("Error parsing candle data at index {} for symbol {}: {}", i, symbol, e.getMessage());
            }
        }

        logger.info("Successfully parsed {} candles for symbol: {}", candles.size(), symbol);
        return candles;
    }

    /**
     * Add throttling to avoid rate limiting with enhanced randomization
     */
    private void throttleRequest() {
        try {
            // Use default values if yahoo config is not available
            int min = (appConfig.yahoo() != null && appConfig.yahoo().throttle() != null) ?
                    appConfig.yahoo().throttle().min() : 1000;
            int max = (appConfig.yahoo() != null && appConfig.yahoo().throttle() != null) ?
                    appConfig.yahoo().throttle().max() : 2000;

            // Add extra randomization to make requests less predictable
            int baseDelay = random.nextInt(max - min + 1) + min;
            int extraRandomDelay = random.nextInt(500); // Add up to 500ms extra randomness
            int totalDelay = baseDelay + extraRandomDelay;

            logger.debug("Throttling request for {} ms", totalDelay);
            Thread.sleep(totalDelay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Throttling interrupted", e);
        }
    }

}
