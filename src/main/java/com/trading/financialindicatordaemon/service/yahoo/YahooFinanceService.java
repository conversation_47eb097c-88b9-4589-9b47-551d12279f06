package com.trading.financialindicatordaemon.service.yahoo;

import com.trading.financialindicatordaemon.client.yahoo.YahooFinanceResponse;
import com.trading.financialindicatordaemon.client.yahoo.YahooIndicators;
import com.trading.financialindicatordaemon.config.AppConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@Service
public class YahooFinanceService {

    private static final Logger logger = LoggerFactory.getLogger(YahooFinanceService.class);

    private final AppConfig appConfig;
    private final Random random = new Random();
    private final PythonYfinanceService pythonYfinanceService;

    public YahooFinanceService(AppConfig appConfig,
                               PythonYfinanceService pythonYfinanceService) {
        this.appConfig = appConfig;
        this.pythonYfinanceService = pythonYfinanceService;
    }

    /**
     * Fetch stock candle data for a given symbol and time period
     */
    public List<YahooStockCandle> getStockCandles(String symbol, long startTimestamp, long endTimestamp) {
        logger.info("Fetching stock data for symbol: {} from {} to {}", symbol, startTimestamp, endTimestamp);

        throttleRequest();

        return pythonYfinanceService.getStockCandles(symbol, startTimestamp, endTimestamp);
    }

    /**
     * Parse Yahoo Finance API response into StockCandle objects
     */
    private List<YahooStockCandle> parseYahooFinanceResponse(String symbol, YahooFinanceResponse response) {
        List<YahooStockCandle> candles = new ArrayList<>();

        if (response == null || response.getChart() == null ||
                response.getChart().getResult() == null || response.getChart().getResult().length == 0) {
            logger.warn("Empty or invalid response for symbol: {}", symbol);
            return candles;
        }

        YahooFinanceResponse.Result result = response.getChart().getResult()[0];

        if (result.getTimestamp() == null || result.getIndicators() == null ||
                result.getIndicators().getQuote() == null || result.getIndicators().getQuote().length == 0) {
            logger.warn("Missing timestamp or quote data for symbol: {}", symbol);
            return candles;
        }

        long[] timestamps = result.getTimestamp();
        YahooIndicators.Quote quote = result.getIndicators().getQuote()[0];

        // Get adjusted close prices if available
        List<BigDecimal> adjCloses = null;
        if (result.getIndicators().getAdjclose() != null && result.getIndicators().getAdjclose().length > 0) {
            adjCloses = result.getIndicators().getAdjclose()[0].getAdjclose();
        }

        // Validate array lengths
        if (timestamps.length != quote.getOpen().size() ||
                timestamps.length != quote.getHigh().size() ||
                timestamps.length != quote.getLow().size() ||
                timestamps.length != quote.getClose().size() ||
                timestamps.length != quote.getVolume().length) {
            logger.error("Mismatched array lengths in Yahoo Finance response for symbol: {}", symbol);
            return candles;
        }

        // Convert arrays to StockCandle objects
        for (int i = 0; i < timestamps.length; i++) {
            try {
                LocalDateTime timestamp = LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(timestamps[i]), ZoneOffset.UTC);

                BigDecimal adjClose = (adjCloses != null && i < adjCloses.size()) ?
                        adjCloses.get(i) : quote.getClose().get(i);

                YahooStockCandle candle = new YahooStockCandle(
                        symbol,
                        timestamp,
                        quote.getOpen().get(i),
                        quote.getHigh().get(i),
                        quote.getLow().get(i),
                        quote.getClose().get(i),
                        adjClose,
                        quote.getVolume()[i],
                        result.getMeta().getCurrency()
                );

                candles.add(candle);

            } catch (Exception e) {
                logger.warn("Error parsing candle data at index {} for symbol {}: {}", i, symbol, e.getMessage());
            }
        }

        logger.info("Successfully parsed {} candles for symbol: {}", candles.size(), symbol);
        return candles;
    }

    /**
     * Add throttling to avoid rate limiting (similar to CMC implementation)
     */
    private void throttleRequest() {
        try {
            // Use default values if yahoo config is not available
            int min = (appConfig.yahoo() != null && appConfig.yahoo().throttle() != null) ?
                    appConfig.yahoo().throttle().min() : 300;
            int max = (appConfig.yahoo() != null && appConfig.yahoo().throttle() != null) ?
                    appConfig.yahoo().throttle().max() : 500;
            int delay = random.nextInt(max - min + 1) + min;
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Throttling interrupted", e);
        }
    }

}
