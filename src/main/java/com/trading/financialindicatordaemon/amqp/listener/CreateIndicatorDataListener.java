package com.trading.financialindicatordaemon.amqp.listener;

import com.rabbitmq.client.Channel;
import com.trading.financialindicatordaemon.config.RabbitMqConfig;
import com.trading.financialindicatordaemon.service.cmc.CmcCandleDataService;
import com.trading.financialindicatordaemon.service.mining.DataMiningService;
import com.trading.financialindicatordaemon.service.indicator.IndicatorDataService;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class CreateIndicatorDataListener extends BaseRabbitMqListener {


    private final IndicatorDataService indicatorDataService;
    private final CmcCandleDataService cmcCandleDataService;

    public CreateIndicatorDataListener(DataMiningService dataMiningService, IndicatorDataService indicatorDataService, CmcCandleDataService cmcCandleDataService) {
        super(dataMiningService);
        this.indicatorDataService = indicatorDataService;
        this.cmcCandleDataService = cmcCandleDataService;
    }

    @RabbitListener(queues = RabbitMqConfig.CREATE_INDICATOR_DATA)
    public void handleCreateIndicatorData(@Payload Map<String, Object> ignoredMessage,
                                          Channel channel,
                                          @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                          Message amqpMessage) {
        handleSimpleMessage("create_indicator_data", channel, deliveryTag, amqpMessage,
                () -> {
                    cmcCandleDataService.findAllSymbolAndConversionCurrency()
                            .forEach(
                                    symbolAndConversionCurrency ->
                                            indicatorDataService.calculateForCrypto(
                                                    symbolAndConversionCurrency.getSymbol(),
                                                    symbolAndConversionCurrency.getConversionCurrency())
                            );

                });
    }

}
