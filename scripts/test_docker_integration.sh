#!/bin/bash

# Test script to verify Docker integration with Python yfinance

set -e

echo "🐳 Testing Docker integration with Python yfinance..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Test 1: Build the test Docker image
echo "📦 Building test Docker image..."
if docker build -f test/Dockerfile.test -t financial-indicator-python-test .; then
    print_status "Test Docker image built successfully"
else
    print_error "Failed to build test Docker image"
    exit 1
fi

# Test 2: Run Python yfinance test in Docker
echo ""
echo "🐍 Testing Python yfinance in Docker container..."
if docker run --rm financial-indicator-python-test bash -c "source venv/bin/activate && python3 scripts/test_yahoo_finance.py"; then
    print_status "Python yfinance test passed in Docker"
else
    print_error "Python yfinance test failed in Docker"
    exit 1
fi

# Test 3: Test actual data fetching
echo ""
echo "📊 Testing Yahoo Finance data fetching in Docker..."
if docker run --rm financial-indicator-python-test bash -c "source venv/bin/activate && python3 scripts/yahoo_finance_fetcher.py AAPL --start 2024-01-01 --end 2024-01-05"; then
    print_status "Yahoo Finance data fetching works in Docker"
else
    print_error "Yahoo Finance data fetching failed in Docker"
    exit 1
fi

# Test 4: Build main application Docker image
echo ""
echo "🏗️  Building main application Docker image..."
if docker build -t financial-indicator-daemon .; then
    print_status "Main application Docker image built successfully"
else
    print_error "Failed to build main application Docker image"
    exit 1
fi

# Test 5: Test with Docker Compose (optional - requires .env file)
echo ""
echo "🐳 Testing Docker Compose integration..."
if [ -f ".env" ]; then
    echo "Found .env file, testing with Docker Compose..."
    if docker-compose --profile test up python-yfinance-test --build --abort-on-container-exit; then
        print_status "Docker Compose test passed"
    else
        print_warning "Docker Compose test failed (this might be due to missing environment variables)"
    fi
    
    # Cleanup
    docker-compose --profile test down
else
    print_warning "No .env file found, skipping Docker Compose test"
    echo "To test with Docker Compose, create a .env file with required variables"
fi

echo ""
echo "🎉 Docker integration tests completed!"
echo ""
echo "Summary:"
echo "✅ Python yfinance works in Docker containers"
echo "✅ Yahoo Finance data fetching works in Docker"
echo "✅ Docker images build successfully"
echo ""
echo "Your Docker setup is ready for the Python yfinance integration!"
echo ""
echo "To run the application with Docker Compose:"
echo "  docker-compose up --build"
echo ""
echo "To test Python integration specifically:"
echo "  docker-compose --profile test up python-yfinance-test --build"
