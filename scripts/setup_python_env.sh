#!/bin/bash

# Setup script for Python yfinance environment
# This script installs the required Python dependencies for Yahoo Finance data fetching

echo "Setting up Python environment for Yahoo Finance data fetching..."

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

# Check Python version
python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "Found Python version: $python_version"

# Install pip if not available
if ! command -v pip3 &> /dev/null; then
    echo "Installing pip..."
    python3 -m ensurepip --upgrade
fi

# Install required packages
echo "Installing Python dependencies..."
pip3 install -r "$(dirname "$0")/requirements.txt"

# Test the installation
echo "Testing yfinance installation..."
python3 -c "
import yfinance as yf
import pandas as pd
print('✓ yfinance imported successfully')
print('✓ pandas imported successfully')

# Test basic functionality
try:
    ticker = yf.Ticker('AAPL')
    info = ticker.info
    print('✓ Basic yfinance functionality works')
except Exception as e:
    print(f'⚠ Warning: yfinance test failed: {e}')
"

echo "Python environment setup complete!"
echo ""
echo "To test the Yahoo Finance fetcher manually, run:"
echo "python3 scripts/yahoo_finance_fetcher.py AAPL --start 2024-01-01 --end 2024-01-31"
