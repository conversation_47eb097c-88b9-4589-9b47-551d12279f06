#!/usr/bin/env python3
"""
Test script to verify yfinance is working correctly
"""

import yfinance as yf
import sys
from datetime import datetime, timedelta

def test_yfinance():
    """Test basic yfinance functionality"""
    print("Testing yfinance functionality...")
    
    try:
        # Test with a popular stock
        symbol = "AAPL"
        print(f"Testing with symbol: {symbol}")
        
        ticker = yf.Ticker(symbol)
        
        # Get recent data (last 30 days)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        print(f"Fetching data from {start_date.date()} to {end_date.date()}")
        
        data = ticker.history(start=start_date, end=end_date, interval='1d')
        
        if data.empty:
            print("❌ No data returned")
            return False
        
        print(f"✅ Successfully fetched {len(data)} days of data")
        print(f"Date range: {data.index[0].date()} to {data.index[-1].date()}")
        print(f"Latest close price: ${data['Close'].iloc[-1]:.2f}")
        
        # Test rate limiting behavior
        print("\nTesting multiple requests...")
        symbols = ["MSFT", "GOOGL", "TSLA"]
        
        for sym in symbols:
            try:
                test_ticker = yf.Ticker(sym)
                test_data = test_ticker.history(period="5d")
                if not test_data.empty:
                    print(f"✅ {sym}: {len(test_data)} days")
                else:
                    print(f"⚠️  {sym}: No data")
            except Exception as e:
                print(f"❌ {sym}: Error - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def check_dependencies():
    """Check if all required dependencies are available"""
    print("Checking dependencies...")
    
    try:
        import yfinance
        print(f"✅ yfinance version: {yfinance.__version__}")
    except ImportError:
        print("❌ yfinance not installed")
        return False
    
    try:
        import pandas
        print(f"✅ pandas version: {pandas.__version__}")
    except ImportError:
        print("❌ pandas not installed")
        return False
    
    try:
        import requests
        print(f"✅ requests available")
    except ImportError:
        print("❌ requests not installed")
        return False
    
    # Check for curl_cffi (optional but recommended)
    try:
        import curl_cffi
        print(f"✅ curl_cffi available (recommended for rate limiting)")
    except ImportError:
        print("⚠️  curl_cffi not available (optional but recommended)")
    
    return True

if __name__ == "__main__":
    print("Yahoo Finance Test Script")
    print("=" * 40)
    
    if not check_dependencies():
        print("\n❌ Missing dependencies. Run: pip install -r requirements.txt")
        sys.exit(1)
    
    print()
    if test_yfinance():
        print("\n✅ All tests passed! yfinance is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Tests failed. yfinance may be experiencing issues.")
        sys.exit(1)
