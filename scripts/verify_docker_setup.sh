#!/bin/bash

# Quick verification script for Docker setup
# This script checks if the Docker configuration is correct without building

set -e

echo "🔍 Verifying Docker setup for Python yfinance integration..."
echo "============================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check 1: Dockerfile exists and has Python setup
echo "📋 Checking Dockerfile..."
if [ -f "Dockerfile" ]; then
    if grep -q "python3" Dockerfile; then
        print_status "Dockerfile contains Python 3 installation"
    else
        print_error "Dockerfile missing Python 3 installation"
        exit 1
    fi
    
    if grep -q "scripts/" Dockerfile; then
        print_status "Dockerfile copies scripts directory"
    else
        print_error "Dockerfile missing scripts directory copy"
        exit 1
    fi
    
    if grep -q "venv" Dockerfile; then
        print_status "Dockerfile creates Python virtual environment"
    else
        print_error "Dockerfile missing virtual environment setup"
        exit 1
    fi
else
    print_error "Dockerfile not found"
    exit 1
fi

# Check 2: Required Python scripts exist
echo ""
echo "🐍 Checking Python scripts..."
required_scripts=(
    "scripts/yahoo_finance_fetcher.py"
    "scripts/requirements.txt"
    "scripts/test_yahoo_finance.py"
)

for script in "${required_scripts[@]}"; do
    if [ -f "$script" ]; then
        print_status "Found $script"
    else
        print_error "Missing $script"
        exit 1
    fi
done

# Check 3: Requirements.txt has necessary dependencies
echo ""
echo "📦 Checking Python requirements..."
if grep -q "yfinance" scripts/requirements.txt; then
    print_status "yfinance dependency found in requirements.txt"
else
    print_error "yfinance missing from requirements.txt"
    exit 1
fi

if grep -q "pandas" scripts/requirements.txt; then
    print_status "pandas dependency found in requirements.txt"
else
    print_error "pandas missing from requirements.txt"
    exit 1
fi

# Check 4: .dockerignore is properly configured
echo ""
echo "🚫 Checking .dockerignore..."
if [ -f ".dockerignore" ]; then
    if grep -q "venv/" .dockerignore; then
        print_status ".dockerignore excludes local venv directory"
    else
        print_warning ".dockerignore should exclude venv/ directory"
    fi
    
    if grep -q "__pycache__" .dockerignore; then
        print_status ".dockerignore excludes Python cache files"
    else
        print_warning ".dockerignore should exclude __pycache__ directories"
    fi
else
    print_warning ".dockerignore file not found"
fi

# Check 5: Docker Compose configuration
echo ""
echo "🐳 Checking Docker Compose..."
if [ -f "docker-compose.yml" ]; then
    print_status "docker-compose.yml found"
    
    if [ -f "docker-compose.override.yml" ]; then
        print_status "docker-compose.override.yml found (development config)"
    else
        print_warning "docker-compose.override.yml not found (optional)"
    fi
else
    print_error "docker-compose.yml not found"
    exit 1
fi

# Check 6: Test Dockerfile
echo ""
echo "🧪 Checking test configuration..."
if [ -f "test/Dockerfile.test" ]; then
    print_status "Test Dockerfile found"
else
    print_warning "test/Dockerfile.test not found (optional)"
fi

# Check 7: Java service integration
echo ""
echo "☕ Checking Java integration..."
java_service="src/main/java/com/trading/financialindicatordaemon/service/yahoo/PythonYfinanceService.java"
if [ -f "$java_service" ]; then
    print_status "PythonYfinanceService.java found"
else
    print_error "PythonYfinanceService.java missing"
    exit 1
fi

# Check 8: Local Python environment (for comparison)
echo ""
echo "🔧 Checking local Python environment..."
if [ -d "venv" ]; then
    print_status "Local Python virtual environment exists"
    
    if [ -f "venv/bin/activate" ]; then
        print_status "Virtual environment activation script found"
    else
        print_error "Virtual environment activation script missing"
    fi
else
    print_warning "Local Python virtual environment not found"
    echo "   Run: python3 -m venv venv && source venv/bin/activate && pip install -r scripts/requirements.txt"
fi

echo ""
echo "🎯 Summary:"
echo "=========="
print_status "Docker configuration is ready for Python yfinance integration"
print_status "All required files are present"
print_status "Dockerfile includes Python 3 and virtual environment setup"
print_status "Python scripts and dependencies are configured"

echo ""
echo "📝 Next steps:"
echo "1. Build the Docker image: docker build -t financial-indicator-daemon ."
echo "2. Test Python integration: docker-compose --profile test up python-yfinance-test --build"
echo "3. Run the application: docker-compose up --build"
echo ""
echo "🚀 Your Docker setup is ready for the Python yfinance integration!"
